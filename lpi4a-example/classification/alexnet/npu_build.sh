#!/bin/bash

hhb -D --model-file alexnet.caffemodel alexnet.prototx --data-scale 1 --data-mean "104 117 124" --board th1520 --postprocess save_and_top5 --input-name "data" --output-name "prob" --input-shape "1 3 227 227" --calibrate-dataset persian_cat.jpg --quantization-scheme "int16_sym" --pixel-format BGR
OPENCV_DIR=/home/<USER>/OpenCV
~/llvm/bin/clang++ main.cpp -I${OPENCV_DIR}/include/opencv4 -L${OPENCV_DIR}/lib -lopencv_imgproc -lopencv_imgcodecs -L${OPENCV_DIR}/lib/opencv4/3rdparty/ -llibjpeg-turbo -llibwebp -llibpng -llibtiff -llibopenjp2 -lopencv_core -ldl -lpthread -lrt -lzlib -lcsi_cv -latomic --static -o alexnet_example
